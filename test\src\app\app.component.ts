// app.component.ts
import { Component } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON>, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { ToggleComponent } from '@ava/play-comp-library';
import { CalendarComponent } from '@ava/play-comp-library';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, ToggleComponent, CalendarComponent],
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css']
})
export class AppComponent {
  onDateSelected(date: Date) {
      console.log('Selected date:', date);
      // Handle the selected date as needed
    }
  }